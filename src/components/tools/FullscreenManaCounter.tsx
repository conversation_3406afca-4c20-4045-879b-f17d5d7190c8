"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import Image from "next/image";

type ManaType = "white" | "blue" | "black" | "red" | "green";

const FullscreenManaCounter = () => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [viewportHeight, setViewportHeight] = useState(0);
  const [manaCount, setManaCount] = useState<Record<ManaType, number>>({
    white: 0,
    blue: 0,
    black: 0,
    red: 0,
    green: 0,
  });

  // Calcola l'altezza reale del viewport escludendo le barre del browser
  useEffect(() => {
    const updateViewportHeight = () => {
      // Usa visualViewport se disponibile (più preciso per mobile)
      if (window.visualViewport) {
        setViewportHeight(window.visualViewport.height);
      } else {
        // Fallback per browser più vecchi
        setViewportHeight(window.innerHeight);
      }
    };

    updateViewportHeight();

    // Ascolta i cambiamenti di dimensione del viewport
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', updateViewportHeight);
      return () => window.visualViewport?.removeEventListener('resize', updateViewportHeight);
    } else {
      window.addEventListener('resize', updateViewportHeight);
      return () => window.removeEventListener('resize', updateViewportHeight);
    }
  }, []);

  const handleManaChange = (type: ManaType, amount: number) => {
    setManaCount(prev => ({
      ...prev,
      [type]: Math.max(0, prev[type] + amount),
    }));
  };

  // Mana configuration with exact colors from reference image
  const manaConfig: Record<ManaType, { bgColor: string; symbol: string }> = {
    white: { bgColor: "#FAFCB3", symbol: "/mana/mana-w.svg" },
    blue: { bgColor: "#57B2F1", symbol: "/mana/mana-u.svg" },
    black: { bgColor: "#707070", symbol: "/mana/mana-b.svg" },
    red: { bgColor: "#F23C44", symbol: "/mana/mana-r.svg" },
    green: { bgColor: "#25A955", symbol: "/mana/mana-g.svg" },
  };

  const manaTypes: ManaType[] = ["white", "blue", "black", "red", "green"];

  const handleBarClick = (type: ManaType, isIncrement: boolean) => {
    handleManaChange(type, isIncrement ? 1 : -1);
  };

  const ManaCounterContent = () => (
    <div className="w-full h-full relative">
      {/* Toggle button - only visible in fullscreen mode */}
      {isFullscreen && (
        <button
          onClick={() => setIsFullscreen(false)}
          className="absolute top-4 right-4 z-50 w-12 h-12 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-colors"
          aria-label="Exit fullscreen"
        >
          ✕
        </button>
      )}

      {/* Mobile layout - horizontal bars stacked vertically */}
      <div className="flex md:hidden w-full h-full flex-col">
        {manaTypes.map((type) => (
          <div
            key={type}
            className="w-full flex-1 relative cursor-pointer select-none"
            style={{
              backgroundColor: manaConfig[type].bgColor,
              height: isFullscreen ? `${viewportHeight / 5}px` : 'auto'
            }}
          >
            {/* Left half - decrement */}
            <div
              className="absolute left-0 top-0 w-1/2 h-full z-10"
              onClick={() => handleBarClick(type, false)}
            />
            {/* Right half - increment */}
            <div
              className="absolute right-0 top-0 w-1/2 h-full z-10"
              onClick={() => handleBarClick(type, true)}
            />
            
            {/* Content */}
            <div className="relative w-full h-full flex items-center justify-between px-6">
              {/* Mana symbol on the left */}
              <div className="flex-shrink-0">
                <Image
                  src={manaConfig[type].symbol}
                  alt={`${type} mana`}
                  width={48}
                  height={48}
                  className="w-12 h-12"
                />
              </div>
              
              {/* Counter in the center */}
              <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <span className="text-4xl font-bold text-black">
                  {manaCount[type]}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Desktop layout - vertical bars side by side */}
      <div className="hidden md:flex w-full h-full">
        {manaTypes.map((type) => (
          <div
            key={type}
            className="flex-1 h-full relative cursor-pointer select-none"
            style={{ backgroundColor: manaConfig[type].bgColor }}
          >
            {/* Upper half - increment */}
            <div
              className="absolute top-0 left-0 w-full h-1/2 z-10"
              onClick={() => handleBarClick(type, true)}
            />
            {/* Lower half - decrement */}
            <div
              className="absolute bottom-0 left-0 w-full h-1/2 z-10"
              onClick={() => handleBarClick(type, false)}
            />
            
            {/* Content */}
            <div className="relative w-full h-full flex flex-col items-center justify-center">
              {/* Mana symbol */}
              <div className="mb-4">
                <Image
                  src={manaConfig[type].symbol}
                  alt={`${type} mana`}
                  width={64}
                  height={64}
                  className="w-16 h-16"
                />
              </div>
              
              {/* Counter */}
              <div>
                <span className="text-5xl font-bold text-black">
                  {manaCount[type]}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  if (isFullscreen) {
    return (
      <div
        className="fixed inset-0 w-screen overflow-hidden"
        style={{
          height: viewportHeight > 0 ? `${viewportHeight}px` : '100vh',
          top: 0,
          left: 0
        }}
      >
        <ManaCounterContent />
      </div>
    );
  }

  return (
    <MainLayout currentPage="tools">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-white">
              Mana Counter
            </h1>
            <p className="text-blue-200 mt-2">
              Tieni traccia del mana disponibile nel tuo pool durante le partite di Magic: The Gathering
            </p>
          </div>
          <button
            onClick={() => setIsFullscreen(true)}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors flex items-center gap-2"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
            </svg>
            Fullscreen
          </button>
        </div>
        
        <div className="bg-black/20 backdrop-blur-sm rounded-lg border border-blue-500/30 overflow-hidden" style={{ height: "60vh" }}>
          <ManaCounterContent />
        </div>
      </div>
    </MainLayout>
  );
};

export default FullscreenManaCounter;
