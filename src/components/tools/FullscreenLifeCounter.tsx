"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";

const FullscreenLifeCounter = () => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [viewportHeight, setViewportHeight] = useState(0);
  const [player1Life, setPlayer1Life] = useState(20);
  const [player2Life, setPlayer2Life] = useState(20);
  const [showSettingsMenu, setShowSettingsMenu] = useState(false);

  // Calcola l'altezza reale del viewport escludendo le barre del browser
  useEffect(() => {
    const updateViewportHeight = () => {
      // Usa visualViewport se disponibile (più preciso per mobile)
      if (window.visualViewport) {
        setViewportHeight(window.visualViewport.height);
      } else {
        // Fallback per browser più vecchi
        setViewportHeight(window.innerHeight);
      }
    };

    updateViewportHeight();

    // Ascolta i cambiamenti di dimensione del viewport
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', updateViewportHeight);
      return () => window.visualViewport?.removeEventListener('resize', updateViewportHeight);
    } else {
      window.addEventListener('resize', updateViewportHeight);
      return () => window.removeEventListener('resize', updateViewportHeight);
    }
  }, []);

  const handleLifeChange = (player: 1 | 2, amount: number) => {
    if (player === 1) {
      setPlayer1Life(prev => prev + amount);
    } else {
      setPlayer2Life(prev => prev + amount);
    }
  };

  const resetLife = () => {
    setPlayer1Life(20);
    setPlayer2Life(20);
  };

  const LifeCounterContent = () => (
    <div className="w-full h-full relative flex flex-col">
      {/* Settings menu - visible in both fullscreen and normal mode */}
      <div className="absolute top-4 right-4 z-50">
        {/* Settings button */}
        <button
          onClick={() => setShowSettingsMenu(!showSettingsMenu)}
          className={`${isFullscreen ? 'w-12 h-12' : 'w-8 h-8'} bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-colors`}
          aria-label="Settings"
        >
          <svg className={`${isFullscreen ? 'w-6 h-6' : 'w-4 h-4'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </button>

        {/* Settings dropdown menu */}
        {showSettingsMenu && (
          <div className="absolute top-12 right-0 bg-black/80 backdrop-blur-sm rounded-lg border border-white/20 p-4 min-w-64">
            <div className="space-y-4">
              {/* Reset button */}
              <div>
                <button
                  onClick={resetLife}
                  className="w-full px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-md transition-colors flex items-center justify-center space-x-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <span>Reset Life (20)</span>
                </button>

                {/* Close button - only in fullscreen */}
                {isFullscreen && (
                  <button
                    onClick={() => setIsFullscreen(false)}
                    className="w-full px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors flex items-center justify-center space-x-2 mt-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    <span>Chiudi Fullscreen</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Player 2 (Invertito) - Metà superiore */}
      <div
        className={`flex-1 flex flex-col justify-center items-center transform rotate-180 transition-colors duration-300 relative ${
          player2Life <= 0
            ? 'bg-red-900/40 border-red-500/50'
            : 'bg-gradient-to-t from-blue-900/30 to-sky-900/30'
        }`}
        style={{ height: isFullscreen ? `${viewportHeight / 2}px` : 'auto' }}
      >
        {/* Area cliccabile sinistra (decremento) */}
        <div
          className="absolute left-0 top-0 w-1/3 h-full z-10 cursor-pointer"
          onClick={() => handleLifeChange(2, -1)}
        />

        {/* Area cliccabile destra (incremento) */}
        <div
          className="absolute right-0 top-0 w-1/3 h-full z-10 cursor-pointer"
          onClick={() => handleLifeChange(2, 1)}
        />

        <div className="w-full max-w-md px-6 py-4 relative z-20">
          <div className="flex justify-between items-center mb-6">
            <button
              onClick={() => handleLifeChange(2, -1)}
              className={`${isFullscreen ? 'w-20 h-20 text-4xl' : 'w-16 h-16 text-3xl'} bg-blue-900/70 hover:bg-blue-800 text-white rounded-full shadow-lg transform transition-transform hover:scale-105 active:scale-95`}
            >
              -
            </button>
            <div className="flex-1 text-center">
              <span className={`${isFullscreen ? 'text-9xl' : 'text-7xl'} font-bold ${player2Life <= 0 ? 'text-red-300' : 'text-white'}`}>
                {player2Life}
              </span>
            </div>
            <button
              onClick={() => handleLifeChange(2, 1)}
              className={`${isFullscreen ? 'w-20 h-20 text-4xl' : 'w-16 h-16 text-3xl'} bg-blue-900/70 hover:bg-blue-800 text-white rounded-full shadow-lg transform transition-transform hover:scale-105 active:scale-95`}
            >
              +
            </button>
          </div>
          <div className="flex justify-center space-x-4">
            <button
              onClick={() => handleLifeChange(2, -5)}
              className={`${isFullscreen ? 'px-6 py-3 text-lg' : 'px-4 py-2'} bg-blue-900/70 hover:bg-blue-800 text-white rounded-md shadow-md transform transition-transform hover:scale-105 active:scale-95`}
            >
              -5
            </button>
            <button
              onClick={() => handleLifeChange(2, 5)}
              className={`${isFullscreen ? 'px-6 py-3 text-lg' : 'px-4 py-2'} bg-blue-900/70 hover:bg-blue-800 text-white rounded-md shadow-md transform transition-transform hover:scale-105 active:scale-95`}
            >
              +5
            </button>
          </div>
        </div>
      </div>
      
      {/* Player 1 (Normale) - Metà inferiore */}
      <div
        className={`flex-1 flex flex-col justify-center items-center transition-colors duration-300 relative ${
          player1Life <= 0
            ? 'bg-red-900/40 border-red-500/50'
            : 'bg-gradient-to-b from-blue-900/30 to-sky-900/30'
        }`}
        style={{ height: isFullscreen ? `${viewportHeight / 2}px` : 'auto' }}
      >
        {/* Area cliccabile sinistra (decremento) */}
        <div
          className="absolute left-0 top-0 w-1/3 h-full z-10 cursor-pointer"
          onClick={() => handleLifeChange(1, -1)}
        />

        {/* Area cliccabile destra (incremento) */}
        <div
          className="absolute right-0 top-0 w-1/3 h-full z-10 cursor-pointer"
          onClick={() => handleLifeChange(1, 1)}
        />

        <div className="w-full max-w-md px-6 py-4 relative z-20">
          <div className="flex justify-between items-center mb-6">
            <button
              onClick={() => handleLifeChange(1, -1)}
              className={`${isFullscreen ? 'w-20 h-20 text-4xl' : 'w-16 h-16 text-3xl'} bg-blue-900/70 hover:bg-blue-800 text-white rounded-full shadow-lg transform transition-transform hover:scale-105 active:scale-95`}
            >
              -
            </button>
            <div className="flex-1 text-center">
              <span className={`${isFullscreen ? 'text-9xl' : 'text-7xl'} font-bold ${player1Life <= 0 ? 'text-red-300' : 'text-white'}`}>
                {player1Life}
              </span>
            </div>
            <button
              onClick={() => handleLifeChange(1, 1)}
              className={`${isFullscreen ? 'w-20 h-20 text-4xl' : 'w-16 h-16 text-3xl'} bg-blue-900/70 hover:bg-blue-800 text-white rounded-full shadow-lg transform transition-transform hover:scale-105 active:scale-95`}
            >
              +
            </button>
          </div>
          <div className="flex justify-center space-x-4">
            <button
              onClick={() => handleLifeChange(1, -5)}
              className={`${isFullscreen ? 'px-6 py-3 text-lg' : 'px-4 py-2'} bg-blue-900/70 hover:bg-blue-800 text-white rounded-md shadow-md transform transition-transform hover:scale-105 active:scale-95`}
            >
              -5
            </button>
            <button
              onClick={() => handleLifeChange(1, 5)}
              className={`${isFullscreen ? 'px-6 py-3 text-lg' : 'px-4 py-2'} bg-blue-900/70 hover:bg-blue-800 text-white rounded-md shadow-md transform transition-transform hover:scale-105 active:scale-95`}
            >
              +5
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  if (isFullscreen) {
    return (
      <div
        className="fixed inset-0 w-screen overflow-hidden bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700"
        style={{
          height: viewportHeight > 0 ? `${viewportHeight}px` : '100vh',
          top: 0,
          left: 0
        }}
      >
        <LifeCounterContent />
      </div>
    );
  }

  return (
    <MainLayout currentPage="tools">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-white">
              Life Counter
            </h1>
            <p className="text-blue-200 mt-2">
              Usa questo strumento per tenere traccia dei punti vita durante le tue partite di Magic: The Gathering
            </p>
          </div>
          <button
            onClick={() => setIsFullscreen(true)}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors flex items-center gap-2"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
            </svg>
            Fullscreen
          </button>
        </div>
        
        <div className="bg-black/20 backdrop-blur-sm rounded-lg border border-blue-500/30 overflow-hidden" style={{ height: "70vh" }}>
          <LifeCounterContent />
        </div>
      </div>
    </MainLayout>
  );
};

export default FullscreenLifeCounter;
